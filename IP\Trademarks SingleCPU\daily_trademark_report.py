#!/usr/bin/env python3
"""
Daily Trademark Report

This script handles the downloading, processing, and database loading of USPTO trademark data.
It supports three modes of operation:
1. Historical Batch Import: One-time bulk import of historical trademark data (pre-2025)
2. Daily Catch-up: Download and process historical daily trademark update files from Jan 1st, 2025
3. Ongoing Daily Processing: Automatically download and process the latest daily trademark update file
"""

import os
import asyncio
import aiohttp # Added aiohttp
import logging
import argparse
import datetime
import tempfile
from dotenv import load_dotenv

# Import local modules
from trademark_parser import parse_daily_trademark_xml
from trademark_db import upsert_trademarks
from trademark_image import download_and_save_image # Changed import
from trademark_file import download_file, extract_zip, send_to_nas, send_folder_to_nas, merge_image_directories

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("daily_trademark_report.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
PRE2025_BASE_URL = "https://data.uspto.gov/ui/datasets/products/files/TRTYRAP/apc18840407-20241231-{:02d}.zip"
DAILY_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/TRTDXFAP/apc{}.zip"

# Directory structure
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
ZIP_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Zip")
os.makedirs(ZIP_DIR, exist_ok=True)
IMAGES_DIR = os.path.join(BASE_DIR, "USPTO_Daily", "Images")
os.makedirs(IMAGES_DIR, exist_ok=True)

# NAS paths
NAS_ZIP_DIR = "/JSLaw/IP TRO Data/IP/Trademarks/USPTO_Daily/Zip"
NAS_IMAGES_DIR = "/JSLaw/IP TRO Data/IP/Trademarks/USPTO_Daily/Images"

# Maximum concurrent tasks
MAX_CONCURRENT_IMAGES = 10      # For downloading images in parallel

# Removed process_trademark_batch function
async def process_zip_file(zip_path, extract_dir, nas_dir):
    """
    Process a single ZIP file: extract, parse XML, update database, and send to NAS.

    Args:
        zip_path (str): Path to the ZIP file
        extract_dir (str): Directory to extract to
        nas_dir (str): NAS directory to send the ZIP file to

    Returns:
        int: Number of trademark records processed
    """
    try:
        # Start NAS transfer task in parallel
        zip_filename = os.path.basename(zip_path)
        nas_path = f"{nas_dir}/{zip_filename}"
        nas_transfer_task = asyncio.create_task(asyncio.to_thread(send_to_nas, zip_path, nas_path))

        # Extract ZIP file - there should be only one XML file per ZIP
        xml_files = extract_zip(zip_path, extract_dir)

        if not xml_files:
            logger.error(f"No XML files found in ZIP: {zip_path}")
            await nas_transfer_task  # Wait for NAS transfer to complete
            return 0

        # We expect only one XML file per ZIP
        xml_file = xml_files[0]

        try:
            # Parse XML content using the file path for memory efficiency
            # The parser will handle opening and reading the file iteratively.
            # Parsing is CPU-bound, run in a thread. It returns trademarks and serials to download.
            trademarks, serials_to_download = await asyncio.to_thread(parse_daily_trademark_xml, xml_file)

            # Check if parsing yielded any trademarks
            if not trademarks:
                logger.warning(f"No trademark records found in XML file: {xml_file}")
                await nas_transfer_task # Wait for NAS transfer to complete
                # No need to check serials_to_download if trademarks is empty
                return 0

            # Create a temporary directory for this XML's images (will be deleted automatically)
            with tempfile.TemporaryDirectory(prefix="tm_images_") as temp_images_dir:
                logger.info(f"Using temporary image directory: {temp_images_dir}")

                # --- Asynchronous Image Downloading ---
                if serials_to_download:
                    logger.info(f"Attempting to download {len(serials_to_download)} images concurrently...")
                    download_semaphore = asyncio.Semaphore(MAX_CONCURRENT_IMAGES)
                    async with aiohttp.ClientSession() as session:
                        download_tasks = [
                            download_and_save_image(session, sn, temp_images_dir, IMAGES_DIR, download_semaphore)
                            for sn in serials_to_download
                        ]
                        # Run downloads and capture results (True/False or Exception)
                        results = await asyncio.gather(*download_tasks, return_exceptions=True)

                    # Create a status map from results
                    download_status = {}
                    for sn, res in zip(serials_to_download, results):
                        if isinstance(res, Exception):
                            logger.error(f"Image download failed for {sn} with exception: {res}")
                            download_status[sn] = False # Treat exception as failure
                        elif isinstance(res, bool):
                            download_status[sn] = res
                        else:
                            logger.error(f"Unexpected result type for {sn} download: {type(res)}")
                            download_status[sn] = False # Treat unexpected as failure

                    logger.info(f"Finished image download attempts. Success count: {sum(1 for status in download_status.values() if status)}")

                    # Update trademark records with download status
                    for trademark in trademarks:
                        sn = trademark.get('ser_no')
                        mark_feature_code = trademark.get('mark_feature_code')
                        # Only update if download was relevant and attempted
                        if mark_feature_code in [2, 3, 5] and sn in download_status:
                            trademark['image_source'] = "USPTO_URL" if download_status[sn] else "NotFound"
                        # Ensure image_source is set even if download wasn't needed/attempted
                        elif 'image_source' not in trademark:
                             trademark['image_source'] = None

                else:
                    logger.info("No images identified for download in this XML.")
                    # Ensure image_source is set to None if not already present
                    for trademark in trademarks:
                        if 'image_source' not in trademark:
                            trademark['image_source'] = None
                # --- End Asynchronous Image Downloading ---

                # The 'trademarks' list now contains updated 'image_source' fields
                total_processed = 0 # Reset total_processed, will be updated by db task

                # Update database in a separate thread (I/O bound) with the final trademark list
                db_task = asyncio.create_task(asyncio.to_thread(upsert_trademarks, trademarks))

                # Send ONLY the temporary image folder to NAS in parallel
                # Check if the temp directory actually contains anything before sending
                if any(os.scandir(temp_images_dir)):
                    logger.info(f"Sending images from temporary directory {temp_images_dir} to NAS.")
                    nas_images_task = asyncio.create_task(asyncio.to_thread(send_folder_to_nas, temp_images_dir, NAS_IMAGES_DIR))
                else:
                    logger.info("No new images downloaded for this XML, skipping NAS image transfer.")
                    nas_images_task = None # No task to await later

                # Wait for database update to complete
                processed = await db_task
                total_processed += processed

                logger.info(f"Processed {processed} trademark records from {xml_file}")

                # Wait for NAS transfers to complete
                await nas_transfer_task
                if nas_images_task:
                    await nas_images_task
                    logger.info("NAS image transfer task completed.")

                    # Move images from temp dir to main images dir using the refactored function
                    if not merge_image_directories(temp_images_dir, IMAGES_DIR):
                        # Log error, but don't stop the whole process if moving fails
                        logger.error(f"Failed to merge images from {temp_images_dir} to {IMAGES_DIR}. Check trademark_file.log for details.")
                else:
                    logger.info("Skipped moving images as no NAS transfer was initiated.")

                # Temporary directory is automatically cleaned up by the 'with' statement exiting

            return total_processed

        except Exception as e:
            logger.error(f"Error processing XML file {xml_file}: {str(e)}")
            await nas_transfer_task  # Wait for NAS transfer to complete
            return 0

        finally:
            # Delete XML file after processing
            if os.path.exists(xml_file):
                os.remove(xml_file)

    except Exception as e:
        logger.error(f"Error processing ZIP file {zip_path}: {str(e)}")
        return 0

async def historical_batch_import():
    """
    Perform a one-time bulk import of historical trademark data (pre-2025).
    """
    logger.info("Starting historical batch import...")

    # Create directories if they don't exist
    os.makedirs(ZIP_DIR, exist_ok=True)

    # Setup API headers
    api_key = os.getenv("USPTO_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Download and process each historical ZIP file
    total_processed = 0

    for i in range(1, 83):  # 82 historical ZIP files
        zip_url = PRE2025_BASE_URL.format(i)
        zip_filename = f"apc18840407-20241231-{i:02d}.zip"
        zip_path = os.path.join(ZIP_DIR, zip_filename)

        logger.info(f"Processing historical file {i}/82: {zip_filename}")

        # Download ZIP file if it doesn't exist
        if not os.path.exists(zip_path):
            logger.info(f"Downloading {zip_filename} from {zip_url}")
            if not download_file(zip_url, zip_path, headers):
                logger.error(f"Failed to download {zip_filename}")
                continue

        # Process ZIP file
        processed = await process_zip_file(zip_path, ZIP_DIR, NAS_ZIP_DIR)
        total_processed += processed

        logger.info(f"Processed {processed} trademark records from {zip_filename}")

    logger.info(f"Historical batch import completed. Total records processed: {total_processed}")

async def process_date(date, headers=None):
    """
    Process a single date: download, extract, parse, update database, and send to NAS.

    Args:
        date (datetime.date): Date to process
        headers (dict, optional): HTTP headers for API requests

    Returns:
        int: Number of trademark records processed
    """
    # Create directories if they don't exist
    os.makedirs(ZIP_DIR, exist_ok=True)

    # Construct file information - format as YYMMDD for daily files
    date_str = date.strftime("%y%m%d")  # Note: lowercase 'y' for 2-digit year
    zip_url = DAILY_BASE_URL.format(date_str)
    zip_filename = f"apc{date_str}.zip"
    zip_path = os.path.join(ZIP_DIR, zip_filename)

    logger.info(f"Processing daily file for {date}: {zip_filename}")

    # Download ZIP file if it doesn't exist
    if not os.path.exists(zip_path):
        logger.info(f"Downloading {zip_filename} from {zip_url}")
        if not download_file(zip_url, zip_path, headers):
            logger.warning(f"No file available for {date}")
            return 0

    # Process ZIP file
    processed = await process_zip_file(zip_path, ZIP_DIR, NAS_ZIP_DIR)

    logger.info(f"Processed {processed} trademark records from {zip_filename}")
    return processed

async def daily_catchup(start_date, end_date=None):
    """
    Download and process historical daily trademark update files from a specified start date.

    Args:
        start_date (datetime.date): Start date for catchup
        end_date (datetime.date, optional): End date for catchup. Defaults to today.
    """
    if end_date is None:
        end_date = datetime.date.today()

    logger.info(f"Starting daily catchup from {start_date} to {end_date}...")

    # Setup API headers
    api_key = os.getenv("USPTO_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Iterate through each date
    current_date = start_date
    total_processed = 0

    while current_date <= end_date:
        processed = await process_date(current_date, headers)
        total_processed += processed

        # Move to next date
        current_date += datetime.timedelta(days=1)

    logger.info(f"Daily catchup completed. Total records processed: {total_processed}")
    return total_processed

async def ongoing_daily_processing(date=None):
    """
    Automatically download and process the latest daily trademark update file.

    Args:
        date (datetime.date, optional): Date to process. Defaults to yesterday.
    """
    if date is None:
        # Default to yesterday
        date = datetime.date.today() - datetime.timedelta(days=1)

    logger.info(f"Starting ongoing daily processing for {date}...")

    # Setup API headers
    api_key = os.getenv("USPTO_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Process the date
    processed = await process_date(date, headers)

    logger.info(f"Ongoing daily processing completed. Records processed: {processed}")
    return processed

async def test_processing():
    """
    Test function to process a specific date (2025-03-15).
    """
    test_date = datetime.date(2025, 3, 15)
    logger.info(f"Running test processing for {test_date}...")

    # Setup API headers
    # api_key = os.getenv("USPTO_BULK_API_KEY")
    api_key = "lmdgnqdrawnvdncavsbdraxrtyimxx"
    headers = {"X-API-KEY": api_key} if api_key else None

    # Process the test date directly
    processed = await process_date(test_date, headers)

    logger.info(f"Test processing completed. Records processed: {processed}")
    return processed

async def test_timeout_urls():
    """
    Test downloading specific URLs that previously timed out.
    """
    urls_to_test = [
        "https://tsdr.uspto.gov/img/79409676/large",
        "https://tsdr.uspto.gov/img/86345847/large",
        "https://tsdr.uspto.gov/img/79358169/large",
        "https://tsdr.uspto.gov/img/75676599/large",
        "https://tsdr.uspto.gov/img/98087874/large",
        "https://tsdr.uspto.gov/img/98726235/large",
        "https://tsdr.uspto.gov/img/79408098/large",
        "https://tsdr.uspto.gov/img/86043382/large",
        "https://tsdr.uspto.gov/img/86256465/large",
        "https://tsdr.uspto.gov/img/79410828/large",
        "https://tsdr.uspto.gov/img/79368020/large",
        "https://tsdr.uspto.gov/img/98536148/large",
    ]
    logger.info(f"Starting test download for {len(urls_to_test)} specific URLs...")

    # Extract serial numbers from URLs for the download function
    serials_to_test = [url.split('/')[-2] for url in urls_to_test]

    # Create a temporary directory for test downloads (optional, could download directly)
    with tempfile.TemporaryDirectory(prefix="tm_test_timeout_") as temp_images_dir:
        logger.info(f"Using temporary image directory for testing: {temp_images_dir}")
        download_semaphore = asyncio.Semaphore(MAX_CONCURRENT_IMAGES) # Use existing constant
        async with aiohttp.ClientSession() as session:
            download_tasks = [
                # Using the existing download function, saving to temp dir
                # We pass IMAGES_DIR as the final destination, though it might not be strictly needed for just testing
                download_and_save_image(session, sn, temp_images_dir, IMAGES_DIR, download_semaphore)
                for sn in serials_to_test
            ]
            results = await asyncio.gather(*download_tasks, return_exceptions=True)

        success_count = 0
        for sn, res, url in zip(serials_to_test, results, urls_to_test):
            if isinstance(res, Exception):
                logger.error(f"Test download FAILED for {sn} ({url}) with exception: {res}")
            elif isinstance(res, bool) and res:
                logger.info(f"Test download SUCCEEDED for {sn} ({url})")
                success_count += 1
            elif isinstance(res, bool) and not res:
                 logger.warning(f"Test download FAILED for {sn} ({url}) (returned False)")
            else:
                logger.error(f"Test download for {sn} ({url}) returned unexpected result: {res}")

        logger.info(f"Finished test downloads. Success count: {success_count}/{len(urls_to_test)}")
        # Optionally, clean up the temp directory if needed, but 'with' handles it.
        # If images were successfully downloaded, they might be in temp_images_dir
        # and potentially moved to IMAGES_DIR depending on download_and_save_image logic.

def main():
    """
    Main function to parse command-line arguments and run the appropriate mode.
    """
    parser = argparse.ArgumentParser(description="USPTO Trademark Data Processing")
    parser.add_argument("--mode", choices=["historical", "catchup", "daily", "test", "test_timeouts"], required=True,
                        help="Mode of operation: historical, catchup, daily, test, or test_timeouts")
    parser.add_argument("--start-date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="Start date for catchup mode (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="End date for catchup mode (YYYY-MM-DD)")
    parser.add_argument("--date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="Specific date for daily mode (YYYY-MM-DD)")

    args = parser.parse_args()

    if args.mode == "historical":
        asyncio.run(historical_batch_import())

    elif args.mode == "catchup":
        if not args.start_date:
            start_date = datetime.date(2025, 1, 1)  # Default to Jan 1st, 2025
        else:
            start_date = args.start_date

        asyncio.run(daily_catchup(start_date, args.end_date))

    elif args.mode == "daily":
        asyncio.run(ongoing_daily_processing(args.date))

    elif args.mode == "test":
        asyncio.run(test_processing())

    elif args.mode == "test_timeouts":
        asyncio.run(test_timeout_urls())

if __name__ == "__main__":
    asyncio.run(test_processing())
    # Call main() so argument parsing works when script is run directly
    main()
